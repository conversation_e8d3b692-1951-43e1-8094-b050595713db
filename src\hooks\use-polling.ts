"use client";

import { useEffect, useRef, useCallback, useState } from "react";

interface UsePollingOptions {
  interval?: number; // 轮询间隔，默认3000ms
  immediate?: boolean; // 是否立即执行，默认true
  enabled?: boolean; // 是否启用轮询，默认true
}

interface UsePollingReturn {
  isPolling: boolean;
  start: () => void;
  stop: () => void;
  toggle: () => void;
  countdown: number; // 距离下次轮询的倒计时（秒）
}

/**
 * 轮询 Hook
 * @param callback 轮询执行的回调函数
 * @param options 轮询配置选项
 * @returns 轮询控制对象
 */
export function usePolling(
  callback: () => void | Promise<void>,
  options: UsePollingOptions = {}
): UsePollingReturn {
  const {
    interval = 3000, // 默认3秒
    immediate = true,
    enabled = true,
  } = options;

  const [isPolling, setIsPolling] = useState(enabled);
  const [countdown, setCountdown] = useState(Math.ceil(interval / 1000));
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const countdownRef = useRef<NodeJS.Timeout | null>(null);
  const callbackRef = useRef(callback);

  // 更新回调函数引用
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // 清理定时器
  const clearTimers = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    if (countdownRef.current) {
      clearInterval(countdownRef.current);
      countdownRef.current = null;
    }
  }, []);

  // 启动倒计时
  const startCountdown = useCallback(() => {
    setCountdown(Math.ceil(interval / 1000));
    
    countdownRef.current = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          return Math.ceil(interval / 1000);
        }
        return prev - 1;
      });
    }, 1000);
  }, [interval]);

  // 启动轮询
  const start = useCallback(() => {
    if (!isPolling) {
      setIsPolling(true);
    }
  }, [isPolling]);

  // 停止轮询
  const stop = useCallback(() => {
    if (isPolling) {
      setIsPolling(false);
      clearTimers();
      setCountdown(0);
    }
  }, [isPolling, clearTimers]);

  // 切换轮询状态
  const toggle = useCallback(() => {
    if (isPolling) {
      stop();
    } else {
      start();
    }
  }, [isPolling, start, stop]);

  // 执行轮询回调
  const executeCallback = useCallback(async () => {
    try {
      await callbackRef.current();
    } catch (error) {
      console.error("轮询回调执行失败:", error);
    }
  }, []);

  // 主轮询逻辑
  useEffect(() => {
    if (!enabled || !isPolling) {
      clearTimers();
      setCountdown(0);
      return;
    }

    // 立即执行一次（如果配置了immediate）
    if (immediate) {
      executeCallback();
    }

    // 启动倒计时
    startCountdown();

    // 设置轮询定时器
    intervalRef.current = setInterval(() => {
      executeCallback();
    }, interval);

    return () => {
      clearTimers();
    };
  }, [enabled, isPolling, interval, immediate, executeCallback, startCountdown, clearTimers]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      clearTimers();
    };
  }, [clearTimers]);

  return {
    isPolling,
    start,
    stop,
    toggle,
    countdown,
  };
}
